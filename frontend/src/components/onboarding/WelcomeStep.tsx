import { useOnboarding } from '../../contexts/OnboardingContext'
import { useAuth } from '../../contexts/AuthContext'
import { ArrowRight, Sparkles } from 'lucide-react'

export default function WelcomeStep() {
  const { nextStep } = useOnboarding()
  const { user } = useAuth()

  return (
    <div className="card-glass text-center space-y-6">
      {/* Logo and branding */}
      <div className="flex justify-center mb-6">
        <div className="relative">
          <div className="w-20 h-20 gradient-aura rounded-2xl flex items-center justify-center shadow-lg">
            <span className="text-white font-bold text-2xl">ORA</span>
          </div>
          <div className="absolute -top-2 -right-2">
            <Sparkles className="w-6 h-6 text-sunbeam-900 animate-pulse" />
          </div>
        </div>
      </div>

      {/* Welcome message */}
      <div className="space-y-4">
        <h1 className="text-3xl font-bold text-eclipse-950">
          Welcome to ORA
        </h1>
        <p className="text-lg text-eclipse-950/80 leading-relaxed">
          Your empathic AI companion that understands emotions and creates meaningful conversations.
        </p>
      </div>

      {/* User greeting */}
      {user?.name && (
        <div className="bg-gradient-to-r from-ardent-50 to-bliss-100 rounded-xl p-4 border border-bliss-200">
          <p className="text-eclipse-950 font-medium">
            Hello, {user.name.split(' ')[0]}! 👋
          </p>
          <p className="text-sm text-eclipse-950/70 mt-1">
            Let's personalize your experience in just a few steps.
          </p>
        </div>
      )}

      {/* Features preview */}
      <div className="grid grid-cols-1 gap-4 my-8">
        <div className="flex items-center space-x-3 p-3 bg-white/50 rounded-lg">
          <div className="w-8 h-8 bg-aura-100 rounded-lg flex items-center justify-center">
            <span className="text-aura-900 text-sm">🎯</span>
          </div>
          <div className="text-left">
            <p className="font-medium text-eclipse-950 text-sm">Personalized Conversations</p>
            <p className="text-xs text-eclipse-950/60">Tailored to your personality</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 p-3 bg-white/50 rounded-lg">
          <div className="w-8 h-8 bg-sunbeam-100 rounded-lg flex items-center justify-center">
            <span className="text-sunbeam-900 text-sm">🎤</span>
          </div>
          <div className="text-left">
            <p className="font-medium text-eclipse-950 text-sm">Voice Interaction</p>
            <p className="text-xs text-eclipse-950/60">Natural voice conversations</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 p-3 bg-white/50 rounded-lg">
          <div className="w-8 h-8 bg-bliss-100 rounded-lg flex items-center justify-center">
            <span className="text-bliss-900 text-sm">💝</span>
          </div>
          <div className="text-left">
            <p className="font-medium text-eclipse-950 text-sm">Emotion Understanding</p>
            <p className="text-xs text-eclipse-950/60">AI that truly listens</p>
          </div>
        </div>
      </div>

      {/* Call to action */}
      <div className="space-y-4">
        <button
          onClick={nextStep}
          className="w-full btn-primary flex items-center justify-center space-x-2 py-4 text-lg"
        >
          <span>Get Started</span>
          <ArrowRight className="w-5 h-5" />
        </button>
        
        <p className="text-xs text-eclipse-950/60">
          Takes less than 2 minutes • Your privacy is protected
        </p>
      </div>
    </div>
  )
}
