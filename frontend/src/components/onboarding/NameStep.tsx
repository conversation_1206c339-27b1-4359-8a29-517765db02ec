import { useState, useEffect } from 'react'
import { useOnboarding } from '../../contexts/OnboardingContext'
import { ArrowR<PERSON>, ArrowLeft, User } from 'lucide-react'

export default function NameStep() {
  const { nextStep, prevStep, updateData, onboardingData } = useOnboarding()
  const [firstName, setFirstName] = useState(onboardingData.firstName || '')
  const [error, setError] = useState('')

  // Validate name input
  const validateName = (name: string): string => {
    if (!name.trim()) {
      return 'Please enter your first name'
    }
    if (name.trim().length < 1) {
      return 'Name must be at least 1 character'
    }
    if (name.trim().length > 24) {
      return 'Name must be 24 characters or less'
    }
    // Check for emoji or special characters
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u
    if (emojiRegex.test(name)) {
      return 'Please use only letters and basic punctuation'
    }
    return ''
  }

  const handleNext = () => {
    const validationError = validateName(firstName)
    if (validationError) {
      setError(validationError)
      return
    }
    
    updateData({ firstName: firstName.trim() })
    nextStep()
  }

  const handleInputChange = (value: string) => {
    setFirstName(value)
    if (error) {
      setError('')
    }
  }

  // Auto-focus input when component mounts
  useEffect(() => {
    const input = document.getElementById('firstName')
    if (input) {
      input.focus()
    }
  }, [])

  return (
    <div className="card-glass space-y-6">
      {/* Header */}
      <div className="text-center space-y-3">
        <div className="w-16 h-16 bg-gradient-to-r from-aura-100 to-sunbeam-100 rounded-2xl flex items-center justify-center mx-auto">
          <User className="w-8 h-8 text-aura-900" />
        </div>
        <h2 className="text-2xl font-bold text-eclipse-950">
          What should ORA call you?
        </h2>
        <p className="text-eclipse-950/70">
          We'll use this to personalize your conversations
        </p>
      </div>

      {/* Input section */}
      <div className="space-y-4">
        <div>
          <label htmlFor="firstName" className="block text-sm font-medium text-eclipse-950 mb-2">
            First Name
          </label>
          <input
            id="firstName"
            type="text"
            value={firstName}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleNext()
              }
            }}
            className={`input text-lg ${error ? 'input-error' : ''}`}
            placeholder="Enter your first name"
            maxLength={24}
            autoComplete="given-name"
          />
          {error && (
            <p className="mt-2 text-sm text-red-600 flex items-center space-x-1">
              <span>⚠️</span>
              <span>{error}</span>
            </p>
          )}
        </div>

        {/* Character count */}
        <div className="flex justify-between items-center text-xs text-eclipse-950/60">
          <span>1-24 characters, no emoji</span>
          <span className={firstName.length > 20 ? 'text-sunbeam-900 font-medium' : ''}>
            {firstName.length}/24
          </span>
        </div>
      </div>

      {/* Preview */}
      {firstName.trim() && !error && (
        <div className="bg-gradient-to-r from-ardent-50 to-bliss-100 rounded-xl p-4 border border-bliss-200">
          <p className="text-eclipse-950 text-center">
            <span className="font-medium">Preview:</span> "Hello, {firstName.trim()}! How are you feeling today?"
          </p>
        </div>
      )}

      {/* Navigation */}
      <div className="flex space-x-3">
        <button
          onClick={prevStep}
          className="btn-ghost flex items-center space-x-2 flex-1"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back</span>
        </button>
        
        <button
          onClick={handleNext}
          disabled={!firstName.trim() || !!error}
          className={`flex items-center space-x-2 flex-2 justify-center ${
            firstName.trim() && !error
              ? 'btn-primary'
              : 'btn-disabled'
          }`}
        >
          <span>Continue</span>
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>

      {/* Help text */}
      <div className="text-center">
        <p className="text-xs text-eclipse-950/50">
          Don't worry, you can change this later in your profile settings
        </p>
      </div>
    </div>
  )
}
