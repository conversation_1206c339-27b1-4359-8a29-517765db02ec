import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useOnboarding } from '../../contexts/OnboardingContext'
import { ArrowLeft, Shield, Check, ExternalLink } from 'lucide-react'
import { LoadingSpinner } from '../ui/LoadingSpinner'

export default function ConsentStep() {
  const { prevStep, updateData, onboardingData, submitOnboarding, isLoading, error } = useOnboarding()
  const navigate = useNavigate()
  const [consents, setConsents] = useState({
    privacyPolicy: onboardingData.consent?.privacyPolicy || false,
    dataProcessing: onboardingData.consent?.dataProcessing || false,
    emotionAnalysis: onboardingData.consent?.emotionAnalysis || false
  })

  const handleConsentChange = (type: keyof typeof consents, value: boolean) => {
    const newConsents = { ...consents, [type]: value }
    setConsents(newConsents)
    updateData({ consent: newConsents })
  }

  const handleSubmit = async () => {
    try {
      await submitOnboarding()
      // Navigate to dashboard on success
      navigate('/dashboard', { replace: true })
    } catch (err) {
      // Error is handled by the context
      console.error('Onboarding submission failed:', err)
    }
  }

  const canProceed = consents.privacyPolicy // Only privacy policy is required

  return (
    <div className="card-glass space-y-6">
      {/* Header */}
      <div className="text-center space-y-3">
        <div className="w-16 h-16 bg-gradient-to-r from-aura-100 to-sunbeam-100 rounded-2xl flex items-center justify-center mx-auto">
          <Shield className="w-8 h-8 text-aura-900" />
        </div>
        <h2 className="text-2xl font-bold text-eclipse-950">
          Privacy & Consent
        </h2>
        <p className="text-eclipse-950/70">
          Your privacy matters. Please review and accept our terms.
        </p>
      </div>

      {/* Privacy summary */}
      <div className="bg-gradient-to-r from-ardent-50 to-bliss-100 rounded-xl p-4 border border-bliss-200">
        <h3 className="font-medium text-eclipse-950 mb-2">What we collect:</h3>
        <ul className="text-sm text-eclipse-950/70 space-y-1">
          <li>• Your name and basic profile information</li>
          <li>• Conversation transcripts (to improve AI responses)</li>
          <li>• Emotion analysis data (to enhance empathy)</li>
          <li>• Usage analytics (to improve the product)</li>
        </ul>
      </div>

      {/* Consent checkboxes */}
      <div className="space-y-4">
        {/* Required: Privacy Policy */}
        <div className="flex items-start space-x-3 p-4 border-2 border-aura-200 rounded-xl bg-white/50">
          <button
            onClick={() => handleConsentChange('privacyPolicy', !consents.privacyPolicy)}
            className={`w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all duration-200 ${
              consents.privacyPolicy
                ? 'bg-aura-900 border-aura-900'
                : 'border-gray-300 hover:border-aura-300'
            }`}
          >
            {consents.privacyPolicy && <Check className="w-4 h-4 text-white" />}
          </button>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <p className="font-medium text-eclipse-950">Privacy Policy</p>
              <span className="text-xs bg-aura-100 text-aura-900 px-2 py-1 rounded-full">Required</span>
            </div>
            <p className="text-sm text-eclipse-950/70 mt-1">
              I agree to the{' '}
              <button className="text-aura-900 hover:underline inline-flex items-center">
                Privacy Policy
                <ExternalLink className="w-3 h-3 ml-1" />
              </button>
              {' '}and Terms of Service
            </p>
          </div>
        </div>

        {/* Optional: Data Processing */}
        <div className="flex items-start space-x-3 p-4 border-2 border-gray-200 rounded-xl bg-white/30">
          <button
            onClick={() => handleConsentChange('dataProcessing', !consents.dataProcessing)}
            className={`w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all duration-200 ${
              consents.dataProcessing
                ? 'bg-sunbeam-900 border-sunbeam-900'
                : 'border-gray-300 hover:border-sunbeam-300'
            }`}
          >
            {consents.dataProcessing && <Check className="w-4 h-4 text-white" />}
          </button>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <p className="font-medium text-eclipse-950">Enhanced Processing</p>
              <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">Optional</span>
            </div>
            <p className="text-sm text-eclipse-950/70 mt-1">
              Allow advanced data processing for personalized recommendations
            </p>
          </div>
        </div>

        {/* Optional: Emotion Analysis */}
        <div className="flex items-start space-x-3 p-4 border-2 border-gray-200 rounded-xl bg-white/30">
          <button
            onClick={() => handleConsentChange('emotionAnalysis', !consents.emotionAnalysis)}
            className={`w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all duration-200 ${
              consents.emotionAnalysis
                ? 'bg-bliss-900 border-bliss-900'
                : 'border-gray-300 hover:border-bliss-300'
            }`}
          >
            {consents.emotionAnalysis && <Check className="w-4 h-4 text-white" />}
          </button>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <p className="font-medium text-eclipse-950">Emotion Insights</p>
              <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">Optional</span>
            </div>
            <p className="text-sm text-eclipse-950/70 mt-1">
              Share emotion analysis data to help improve AI empathy
            </p>
          </div>
        </div>
      </div>

      {/* Error display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
          <p className="text-red-800 text-sm flex items-center space-x-2">
            <span>⚠️</span>
            <span>{error}</span>
          </p>
        </div>
      )}

      {/* Navigation */}
      <div className="flex space-x-3">
        <button
          onClick={prevStep}
          disabled={isLoading}
          className="btn-ghost flex items-center space-x-2 flex-1"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back</span>
        </button>
        
        <button
          onClick={handleSubmit}
          disabled={!canProceed || isLoading}
          className={`flex items-center space-x-2 flex-2 justify-center ${
            canProceed && !isLoading ? 'btn-primary' : 'btn-disabled'
          }`}
        >
          {isLoading ? (
            <>
              <LoadingSpinner size="sm" />
              <span>Setting up...</span>
            </>
          ) : (
            <>
              <span>Complete Setup</span>
              <Check className="w-4 h-4" />
            </>
          )}
        </button>
      </div>

      {/* Help text */}
      <div className="text-center">
        <p className="text-xs text-eclipse-950/50">
          You can change these preferences anytime in your profile settings
        </p>
      </div>
    </div>
  )
}
