import { useState } from 'react'
import { useOnboarding, PERSONA_QUESTIONS, calculatePersonaType } from '../../contexts/OnboardingContext'
import { ArrowR<PERSON>, ArrowLeft, Brain, SkipForward } from 'lucide-react'

const PERSONA_DESCRIPTIONS = {
  creative: {
    title: 'Creative Explorer',
    description: 'You love innovation, artistic expression, and thinking outside the box.',
    emoji: '🎨'
  },
  analytical: {
    title: 'Logical Thinker',
    description: 'You prefer systematic approaches, data-driven decisions, and clear reasoning.',
    emoji: '🔬'
  },
  social: {
    title: 'People Connector',
    description: 'You thrive on relationships, collaboration, and shared experiences.',
    emoji: '🤝'
  },
  adventurous: {
    title: 'Bold Explorer',
    description: 'You enjoy taking risks, trying new things, and pushing boundaries.',
    emoji: '🚀'
  },
  thoughtful: {
    title: 'Reflective Mind',
    description: 'You value deep thinking, careful consideration, and meaningful insights.',
    emoji: '🧘'
  }
}

export default function PersonaStep() {
  const { nextStep, prevStep, updateData, onboardingData } = useOnboarding()
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [showResult, setShowResult] = useState(false)
  const [calculatedPersona, setCalculatedPersona] = useState<keyof typeof PERSONA_DESCRIPTIONS | null>(null)

  const handleAnswer = (questionId: string, answer: string) => {
    const newAnswers = { ...answers, [questionId]: answer }
    setAnswers(newAnswers)

    if (currentQuestion < PERSONA_QUESTIONS.length - 1) {
      // Move to next question
      setTimeout(() => {
        setCurrentQuestion(prev => prev + 1)
      }, 300)
    } else {
      // Calculate persona and show result
      const persona = calculatePersonaType(newAnswers)
      setCalculatedPersona(persona)
      setShowResult(true)
    }
  }

  const handleNext = () => {
    if (calculatedPersona) {
      updateData({ personaType: calculatedPersona })
    }
    nextStep()
  }

  const handleSkip = () => {
    updateData({ personaType: 'thoughtful' }) // Default persona
    nextStep()
  }

  const resetAssessment = () => {
    setCurrentQuestion(0)
    setAnswers({})
    setShowResult(false)
    setCalculatedPersona(null)
  }

  if (showResult && calculatedPersona) {
    const persona = PERSONA_DESCRIPTIONS[calculatedPersona]
    return (
      <div className="card-glass space-y-6">
        {/* Result header */}
        <div className="text-center space-y-4">
          <div className="w-20 h-20 bg-gradient-to-r from-bliss-100 to-ardent-50 rounded-2xl flex items-center justify-center mx-auto text-3xl">
            {persona.emoji}
          </div>
          <div>
            <h2 className="text-2xl font-bold text-eclipse-950">
              You're a {persona.title}!
            </h2>
            <p className="text-eclipse-950/70 mt-2">
              {persona.description}
            </p>
          </div>
        </div>

        {/* Persona benefits */}
        <div className="bg-gradient-to-r from-ardent-50 to-bliss-100 rounded-xl p-4 border border-bliss-200">
          <p className="text-sm font-medium text-eclipse-950 mb-2">
            How this helps your ORA experience:
          </p>
          <ul className="text-xs text-eclipse-950/70 space-y-1">
            <li>• Conversations tailored to your thinking style</li>
            <li>• AI responses that match your communication preferences</li>
            <li>• Topics and examples relevant to your interests</li>
          </ul>
        </div>

        {/* Navigation */}
        <div className="space-y-3">
          <button
            onClick={handleNext}
            className="w-full btn-primary flex items-center justify-center space-x-2"
          >
            <span>Perfect! Continue</span>
            <ArrowRight className="w-4 h-4" />
          </button>
          
          <button
            onClick={resetAssessment}
            className="w-full btn-ghost text-sm"
          >
            Retake Assessment
          </button>
        </div>
      </div>
    )
  }

  const question = PERSONA_QUESTIONS[currentQuestion]

  return (
    <div className="card-glass space-y-6">
      {/* Header */}
      <div className="text-center space-y-3">
        <div className="w-16 h-16 bg-gradient-to-r from-bliss-100 to-ardent-50 rounded-2xl flex items-center justify-center mx-auto">
          <Brain className="w-8 h-8 text-bliss-900" />
        </div>
        <h2 className="text-2xl font-bold text-eclipse-950">
          Let's understand your style
        </h2>
        <p className="text-eclipse-950/70">
          Quick assessment to personalize your AI interactions
        </p>
      </div>

      {/* Progress */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm text-eclipse-950/60">
          <span>Question {currentQuestion + 1} of {PERSONA_QUESTIONS.length}</span>
          <span>{Math.round(((currentQuestion + 1) / PERSONA_QUESTIONS.length) * 100)}%</span>
        </div>
        <div className="w-full bg-white/30 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-bliss-900 to-sunbeam-900 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentQuestion + 1) / PERSONA_QUESTIONS.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Question */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-eclipse-950 text-center">
          {question.question}
        </h3>
        
        <div className="space-y-3">
          {question.options.map((option) => (
            <button
              key={option.value}
              onClick={() => handleAnswer(question.id, option.value)}
              className="w-full p-4 rounded-xl border-2 border-gray-200 bg-white/50 hover:border-bliss-300 hover:bg-white/80 transition-all duration-200 text-left"
            >
              <p className="font-medium text-eclipse-950">{option.label}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex space-x-3">
        <button
          onClick={currentQuestion === 0 ? prevStep : () => setCurrentQuestion(prev => prev - 1)}
          className="btn-ghost flex items-center space-x-2 flex-1"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back</span>
        </button>
        
        <button
          onClick={handleSkip}
          className="btn-ghost flex items-center space-x-2 flex-1"
        >
          <SkipForward className="w-4 h-4" />
          <span>Skip</span>
        </button>
      </div>

      {/* Help text */}
      <div className="text-center">
        <p className="text-xs text-eclipse-950/50">
          Optional assessment • You can skip or change this later
        </p>
      </div>
    </div>
  )
}
