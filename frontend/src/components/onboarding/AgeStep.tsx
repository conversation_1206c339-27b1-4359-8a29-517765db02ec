import { useState } from 'react'
import { useOnboarding } from '../../contexts/OnboardingContext'
import { <PERSON>R<PERSON>, ArrowLeft, Calendar } from 'lucide-react'

const AGE_RANGES = [
  { value: '13-17', label: '13-17', description: 'Teen' },
  { value: '18-24', label: '18-24', description: 'Young Adult' },
  { value: '25-34', label: '25-34', description: 'Adult' },
  { value: '35-44', label: '35-44', description: 'Adult' },
  { value: '45-54', label: '45-54', description: 'Adult' },
  { value: '55-64', label: '55-64', description: 'Adult' },
  { value: '65+', label: '65+', description: 'Senior' }
]

export default function AgeStep() {
  const { nextStep, prevStep, updateData, onboardingData } = useOnboarding()
  const [selectedAge, setSelectedAge] = useState(onboardingData.ageRange || '')

  const handleNext = () => {
    if (!selectedAge) return
    
    updateData({ ageRange: selectedAge })
    nextStep()
  }

  const handleAgeSelect = (ageRange: string) => {
    setSelectedAge(ageRange)
  }

  return (
    <div className="card-glass space-y-6">
      {/* Header */}
      <div className="text-center space-y-3">
        <div className="w-16 h-16 bg-gradient-to-r from-sunbeam-100 to-bliss-100 rounded-2xl flex items-center justify-center mx-auto">
          <Calendar className="w-8 h-8 text-sunbeam-900" />
        </div>
        <h2 className="text-2xl font-bold text-eclipse-950">
          What's your age range?
        </h2>
        <p className="text-eclipse-950/70">
          This helps us tailor conversations to be more relevant for you
        </p>
      </div>

      {/* Age range selection */}
      <div className="space-y-3">
        {AGE_RANGES.map((range) => (
          <button
            key={range.value}
            onClick={() => handleAgeSelect(range.value)}
            className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left ${
              selectedAge === range.value
                ? 'border-aura-900 bg-gradient-to-r from-aura-50 to-sunbeam-50 shadow-md'
                : 'border-gray-200 bg-white/50 hover:border-aura-300 hover:bg-white/80'
            }`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-eclipse-950">{range.label}</p>
                <p className="text-sm text-eclipse-950/60">{range.description}</p>
              </div>
              <div className={`w-5 h-5 rounded-full border-2 transition-all duration-200 ${
                selectedAge === range.value
                  ? 'border-aura-900 bg-aura-900'
                  : 'border-gray-300'
              }`}>
                {selectedAge === range.value && (
                  <div className="w-full h-full rounded-full bg-white scale-50"></div>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Privacy note */}
      <div className="bg-gradient-to-r from-ardent-50 to-bliss-100 rounded-xl p-4 border border-bliss-200">
        <div className="flex items-start space-x-2">
          <span className="text-bliss-900 text-sm">🔒</span>
          <div>
            <p className="text-sm font-medium text-eclipse-950">Privacy Protected</p>
            <p className="text-xs text-eclipse-950/70 mt-1">
              We only store your age range, not your exact age. This information is used solely to improve your conversation experience.
            </p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex space-x-3">
        <button
          onClick={prevStep}
          className="btn-ghost flex items-center space-x-2 flex-1"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back</span>
        </button>
        
        <button
          onClick={handleNext}
          disabled={!selectedAge}
          className={`flex items-center space-x-2 flex-2 justify-center ${
            selectedAge ? 'btn-primary' : 'btn-disabled'
          }`}
        >
          <span>Continue</span>
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>

      {/* Help text */}
      <div className="text-center">
        <p className="text-xs text-eclipse-950/50">
          This helps us understand generational preferences and communication styles
        </p>
      </div>
    </div>
  )
}
