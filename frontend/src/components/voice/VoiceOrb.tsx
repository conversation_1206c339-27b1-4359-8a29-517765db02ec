import React, { useEffect, useRef, useState } from 'react';
import { useVoiceStateVisuals, useEmotionAnimations } from '../../hooks/useEmotionVisuals';

export interface VoiceOrbProps {
  emotions?: Record<string, number>;
  isListening?: boolean;
  isProcessing?: boolean;
  isSpeaking?: boolean;
  isIdle?: boolean;
  intensity?: number;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export default function VoiceOrb({
  emotions = {},
  isListening = false,
  isProcessing = false,
  isSpeaking = false,
  isIdle = true,
  intensity = 0.5,
  size = 'large',
  className = ''
}: VoiceOrbProps) {
  const orbRef = useRef<HTMLDivElement>(null);
  const [currentState, setCurrentState] = useState<'idle' | 'listening' | 'processing' | 'speaking'>('idle');

  // Use the enhanced emotion visuals hook
  const voiceVisuals = useVoiceStateVisuals(isListening, isProcessing, isSpeaking, emotions);
  const emotionAnimations = useEmotionAnimations(emotions, !isIdle);

  // Size configurations
  const sizeConfig = {
    small: { width: 120, height: 120, glowSize: 160 },
    medium: { width: 180, height: 180, glowSize: 240 },
    large: { width: 240, height: 240, glowSize: 320 }
  };

  const config = sizeConfig[size];

  // Update current state based on props
  useEffect(() => {
    if (isSpeaking) {
      setCurrentState('speaking');
    } else if (isProcessing) {
      setCurrentState('processing');
    } else if (isListening) {
      setCurrentState('listening');
    } else {
      setCurrentState('idle');
    }
  }, [isListening, isProcessing, isSpeaking, isIdle]);

  // Get state-specific animations and effects using the new emotion system
  const getStateEffects = () => {
    const baseIntensity = voiceVisuals.intensity * intensity;
    const animationSpeed = emotionAnimations.animationSpeed;

    switch (currentState) {
      case 'listening':
        return {
          scale: emotionAnimations.scaleVariation * (1.1 + (baseIntensity * 0.2)),
          pulseSpeed: `${1.5 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity,
          rotationSpeed: `${8 / animationSpeed}s`,
          innerGlow: true,
          pulseRings: true,
          glowRadius: voiceVisuals.glowRadius
        };
      case 'processing':
        return {
          scale: emotionAnimations.scaleVariation * (1.0 + (baseIntensity * 0.15)),
          pulseSpeed: `${2 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.9,
          rotationSpeed: `${4 / animationSpeed}s`,
          innerGlow: true,
          swirling: true,
          glowRadius: voiceVisuals.glowRadius
        };
      case 'speaking':
        return {
          scale: emotionAnimations.scaleVariation * (1.15 + (baseIntensity * 0.25)),
          pulseSpeed: `${1 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity,
          rotationSpeed: `${6 / animationSpeed}s`,
          innerGlow: true,
          expanding: true,
          glowRadius: voiceVisuals.glowRadius
        };
      default: // idle
        return {
          scale: emotionAnimations.scaleVariation,
          pulseSpeed: `${4 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.6,
          rotationSpeed: `${12 / animationSpeed}s`,
          innerGlow: false,
          gentle: true,
          glowRadius: voiceVisuals.glowRadius * 0.5
        };
    }
  };

  const effects = getStateEffects();

  return (
    <div 
      className={`relative flex items-center justify-center ${className}`}
      style={{ 
        width: config.glowSize, 
        height: config.glowSize 
      }}
    >
      {/* Otherworldly Light Wave Emissions */}
      {(isListening || isProcessing || isSpeaking) && (
        <>
          {/* Massive outer energy field */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 2.5,
              height: config.glowSize * 2.5,
              background: `radial-gradient(circle,
                rgba(147, 197, 253, ${effects.glowIntensity * 0.15}) 0%,
                rgba(196, 181, 253, ${effects.glowIntensity * 0.1}) 30%,
                transparent 70%
              )`,
              animationDuration: `${3 / emotionAnimations.animationSpeed}s`,
              animationDelay: '0s',
              filter: 'blur(8px)'
            }}
          />

          {/* Secondary energy wave */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 2,
              height: config.glowSize * 2,
              background: `radial-gradient(circle,
                rgba(165, 243, 252, ${effects.glowIntensity * 0.2}) 0%,
                rgba(147, 197, 253, ${effects.glowIntensity * 0.15}) 40%,
                transparent 80%
              )`,
              animationDuration: `${2.5 / emotionAnimations.animationSpeed}s`,
              animationDelay: '0.5s',
              filter: 'blur(6px)'
            }}
          />

          {/* Primary energy ring */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 1.5,
              height: config.glowSize * 1.5,
              background: `radial-gradient(circle,
                rgba(251, 207, 232, ${effects.glowIntensity * 0.3}) 0%,
                rgba(196, 181, 253, ${effects.glowIntensity * 0.2}) 50%,
                transparent 90%
              )`,
              animationDuration: `${2 / emotionAnimations.animationSpeed}s`,
              animationDelay: '1s',
              filter: 'blur(4px)'
            }}
          />

          {/* Inner intense glow */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 1.2,
              height: config.glowSize * 1.2,
              background: `radial-gradient(circle,
                rgba(147, 197, 253, ${effects.glowIntensity * 0.4}) 0%,
                rgba(165, 243, 252, ${effects.glowIntensity * 0.3}) 60%,
                transparent 100%
              )`,
              animationDuration: `${1.5 / emotionAnimations.animationSpeed}s`,
              animationDelay: '0.2s',
              filter: 'blur(2px)'
            }}
          />
        </>
      )}

      {/* Continuous ambient glow for all states */}
      <div
        className="absolute rounded-full"
        style={{
          width: config.glowSize * 1.8,
          height: config.glowSize * 1.8,
          background: `radial-gradient(circle,
            rgba(147, 197, 253, ${0.1 + effects.glowIntensity * 0.1}) 0%,
            rgba(196, 181, 253, ${0.08 + effects.glowIntensity * 0.08}) 40%,
            rgba(251, 207, 232, ${0.05 + effects.glowIntensity * 0.05}) 70%,
            transparent 100%
          )`,
          filter: 'blur(12px)',
          animation: `orb-ambient-glow ${8 / emotionAnimations.animationSpeed}s ease-in-out infinite`
        }}
      />

      {/* Main orb container */}
      <div
        ref={orbRef}
        className="relative rounded-full transition-all duration-500 ease-out"
        style={{
          width: config.width,
          height: config.height,
          transform: `scale(${effects.scale})`,
          filter: `
            drop-shadow(0 0 ${60 * effects.glowIntensity}px rgba(147, 197, 253, ${effects.glowIntensity * 0.8}))
            drop-shadow(0 0 ${40 * effects.glowIntensity}px rgba(196, 181, 253, ${effects.glowIntensity * 0.6}))
            drop-shadow(0 0 ${80 * effects.glowIntensity}px rgba(165, 243, 252, ${effects.glowIntensity * 0.4}))
            drop-shadow(0 0 ${20 * effects.glowIntensity}px rgba(251, 207, 232, ${effects.glowIntensity * 0.7}))
          `
        }}
      >
        {/* Base glass orb with vibrant gradients */}
        <div
          className="absolute inset-0 rounded-full transition-all duration-1000 ease-out"
          style={{
            background: `
              radial-gradient(circle at 30% 30%,
                rgba(147, 197, 253, 0.9) 0%,
                rgba(196, 181, 253, 0.8) 25%,
                rgba(251, 207, 232, 0.7) 50%,
                rgba(165, 243, 252, 0.6) 75%,
                rgba(147, 197, 253, 0.5) 100%
              )
            `,
            opacity: 0.85 + (voiceVisuals.intensity * 0.15),
            animation: `orb-rotation ${effects.rotationSpeed} linear infinite`,
            filter: `brightness(${1.1 + emotionAnimations.colorIntensity * 0.3}) saturate(${1.4 + voiceVisuals.intensity * 0.6}) blur(0.5px)`,
            backdropFilter: 'blur(8px)'
          }}
        />

        {/* Secondary iridescent layer */}
        <div
          className="absolute inset-0 rounded-full transition-all duration-1000 ease-out"
          style={{
            background: `
              conic-gradient(from 0deg at 50% 50%,
                rgba(147, 197, 253, 0.6) 0deg,
                rgba(196, 181, 253, 0.7) 60deg,
                rgba(251, 207, 232, 0.8) 120deg,
                rgba(165, 243, 252, 0.7) 180deg,
                rgba(147, 197, 253, 0.6) 240deg,
                rgba(196, 181, 253, 0.7) 300deg,
                rgba(147, 197, 253, 0.6) 360deg
              )
            `,
            opacity: 0.6 + (voiceVisuals.intensity * 0.2),
            animation: `orb-rotation ${effects.rotationSpeed} linear infinite reverse`,
            filter: `brightness(${1.2 + emotionAnimations.colorIntensity * 0.2}) saturate(1.6)`
          }}
        />

        {/* Accent shimmer overlay */}
        <div
          className="absolute inset-0 rounded-full transition-all duration-1000 ease-out"
          style={{
            background: `
              linear-gradient(45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 70%
              )
            `,
            opacity: 0.3 + (voiceVisuals.intensity * 0.3),
            animation: `orb-shimmer ${effects.pulseSpeed} ease-in-out infinite`,
            filter: `brightness(${1.3 + emotionAnimations.colorIntensity * 0.2})`
          }}
        />

        {/* Primary glass reflection */}
        <div
          className="absolute inset-0 rounded-full"
          style={{
            background: `
              radial-gradient(ellipse at 25% 25%,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(255, 255, 255, 0.7) 15%,
                rgba(255, 255, 255, 0.3) 35%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 65%
              )
            `,
            filter: 'blur(0.5px)'
          }}
        />

        {/* Secondary highlight streak */}
        <div
          className="absolute inset-1 rounded-full"
          style={{
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.8) 0%,
                rgba(255, 255, 255, 0.4) 25%,
                transparent 50%
              )
            `,
            clipPath: 'ellipse(40% 60% at 30% 30%)'
          }}
        />

        {/* Inner glow rim */}
        <div
          className="absolute inset-1 rounded-full"
          style={{
            background: `
              radial-gradient(circle at 50% 50%,
                transparent 70%,
                rgba(255, 255, 255, 0.4) 85%,
                rgba(255, 255, 255, 0.8) 95%,
                transparent 100%
              )
            `
          }}
        />

        {/* Bottom depth shadow */}
        <div
          className="absolute inset-0 rounded-full"
          style={{
            background: `
              radial-gradient(ellipse at 50% 85%,
                rgba(59, 130, 246, 0.3) 0%,
                rgba(59, 130, 246, 0.15) 30%,
                rgba(147, 51, 234, 0.1) 50%,
                transparent 70%
              )
            `
          }}
        />

        {/* Inner glow for active states */}
        {effects.innerGlow && (
          <div
            className="absolute inset-1 rounded-full animate-pulse"
            style={{
              background: `
                radial-gradient(circle at 50% 50%,
                  rgba(147, 197, 253, 0.4) 0%,
                  rgba(196, 181, 253, 0.3) 50%,
                  transparent 100%
                )
              `,
              opacity: 0.5 * effects.glowIntensity,
              animationDuration: effects.pulseSpeed,
              filter: 'blur(2px)'
            }}
          />
        )}

        {/* Swirling effect for processing */}
        {effects.swirling && (
          <div
            className="absolute inset-0 rounded-full"
            style={{
              background: `
                conic-gradient(from 0deg,
                  transparent 0deg,
                  rgba(147, 197, 253, 0.3) 90deg,
                  rgba(196, 181, 253, 0.4) 180deg,
                  rgba(251, 207, 232, 0.3) 270deg,
                  transparent 360deg
                )
              `,
              animation: `orb-swirl 2s linear infinite`,
              filter: 'blur(1px)'
            }}
          />
        )}

        {/* Expanding rings for speaking */}
        {effects.expanding && (
          <>
            {/* Massive outer speech wave */}
            <div
              className="absolute rounded-full animate-ping"
              style={{
                width: '150%',
                height: '150%',
                top: '-25%',
                left: '-25%',
                background: `radial-gradient(circle,
                  transparent 60%,
                  rgba(147, 197, 253, 0.3) 70%,
                  rgba(147, 197, 253, 0.1) 85%,
                  transparent 100%
                )`,
                animationDuration: '0.6s',
                filter: 'blur(2px)'
              }}
            />

            {/* Medium speech wave */}
            <div
              className="absolute rounded-full animate-ping"
              style={{
                width: '130%',
                height: '130%',
                top: '-15%',
                left: '-15%',
                background: `radial-gradient(circle,
                  transparent 50%,
                  rgba(196, 181, 253, 0.4) 65%,
                  rgba(196, 181, 253, 0.2) 80%,
                  transparent 100%
                )`,
                animationDuration: '0.8s',
                animationDelay: '0.1s',
                filter: 'blur(1.5px)'
              }}
            />

            {/* Inner speech wave */}
            <div
              className="absolute rounded-full animate-ping"
              style={{
                width: '120%',
                height: '120%',
                top: '-10%',
                left: '-10%',
                background: `radial-gradient(circle,
                  transparent 40%,
                  rgba(251, 207, 232, 0.5) 60%,
                  rgba(251, 207, 232, 0.3) 75%,
                  transparent 100%
                )`,
                animationDuration: '1.0s',
                animationDelay: '0.2s',
                filter: 'blur(1px)'
              }}
            />
          </>
        )}
      </div>

      {/* Floating particles for enhanced effect */}
      {(isSpeaking || isProcessing || isListening) && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {[...Array(8)].map((_, i) => {
            const colors = [
              'rgba(147, 197, 253, 0.6)',
              'rgba(196, 181, 253, 0.7)',
              'rgba(251, 207, 232, 0.5)',
              'rgba(165, 243, 252, 0.6)'
            ];
            return (
              <div
                key={i}
                className="absolute rounded-full animate-float"
                style={{
                  width: `${2 + (i % 3)}px`,
                  height: `${2 + (i % 3)}px`,
                  background: colors[i % colors.length],
                  left: `${15 + (i * 8)}%`,
                  top: `${25 + (i * 7)}%`,
                  animationDelay: `${i * 0.4}s`,
                  animationDuration: `${2.5 + (i * 0.3)}s`,
                  filter: 'blur(0.5px)',
                  boxShadow: `0 0 ${4 + (i % 2) * 2}px ${colors[i % colors.length]}`
                }}
              />
            );
          })}
        </div>
      )}

      <style jsx>{`
        @keyframes orb-rotation {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes orb-pulse {
          0%, 100% { opacity: 0.4; transform: scale(1); }
          50% { opacity: 0.7; transform: scale(1.05); }
        }

        @keyframes orb-swirl {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes orb-shimmer {
          0%, 100% {
            transform: translateX(-100%) rotate(0deg);
            opacity: 0.3;
          }
          50% {
            transform: translateX(100%) rotate(180deg);
            opacity: 0.8;
          }
        }

        @keyframes orb-ambient-glow {
          0%, 100% {
            opacity: 0.6;
            transform: scale(1) rotate(0deg);
          }
          25% {
            opacity: 0.8;
            transform: scale(1.05) rotate(90deg);
          }
          50% {
            opacity: 1;
            transform: scale(1.1) rotate(180deg);
          }
          75% {
            opacity: 0.9;
            transform: scale(1.05) rotate(270deg);
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) translateX(0px) scale(1) rotate(0deg);
            opacity: 0.3;
          }
          20% {
            transform: translateY(-15px) translateX(8px) scale(1.1) rotate(72deg);
            opacity: 0.7;
          }
          40% {
            transform: translateY(-25px) translateX(-3px) scale(0.9) rotate(144deg);
            opacity: 1;
          }
          60% {
            transform: translateY(-30px) translateX(12px) scale(1.2) rotate(216deg);
            opacity: 0.8;
          }
          80% {
            transform: translateY(-15px) translateX(-8px) scale(0.8) rotate(288deg);
            opacity: 0.5;
          }
        }
      `}</style>
    </div>
  );
}
