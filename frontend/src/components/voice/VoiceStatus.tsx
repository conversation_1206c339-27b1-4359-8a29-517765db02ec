import { Wifi, WifiOff, Mic, Volume2, Clock } from 'lucide-react'

interface VoiceStatusProps {
  isConnected: boolean
  isConnecting: boolean
  isChatActive: boolean
  isRecording: boolean
  isPlaying: boolean
}

export default function VoiceStatus({
  isConnected,
  isConnecting,
  isChatActive,
  isRecording,
  isPlaying
}: VoiceStatusProps) {
  const getStatusInfo = () => {
    if (isConnecting) {
      return {
        icon: Clock,
        text: 'Connecting to ORA...',
        color: 'text-yellow-400',
        bgColor: 'bg-yellow-400/20'
      }
    }
    
    if (!isConnected) {
      return {
        icon: WifiOff,
        text: 'Connection lost',
        color: 'text-red-400',
        bgColor: 'bg-red-400/20'
      }
    }
    
    if (!isChatActive) {
      return {
        icon: Wifi,
        text: 'Starting conversation...',
        color: 'text-blue-400',
        bgColor: 'bg-blue-400/20'
      }
    }
    
    if (isRecording) {
      return {
        icon: Mic,
        text: 'Listening...',
        color: 'text-red-400',
        bgColor: 'bg-red-400/20'
      }
    }
    
    if (isPlaying) {
      return {
        icon: Volume2,
        text: 'ORA is speaking...',
        color: 'text-green-400',
        bgColor: 'bg-green-400/20'
      }
    }
    
    return {
      icon: Mic,
      text: 'Ready to listen',
      color: 'text-white',
      bgColor: 'bg-white/20'
    }
  }

  const status = getStatusInfo()
  const Icon = status.icon

  return (
    <div className="flex items-center justify-center">
      <div className={`
        flex items-center space-x-3 px-6 py-3 rounded-full backdrop-blur-md
        ${status.bgColor} border border-white/20
      `}>
        <Icon className={`w-5 h-5 ${status.color}`} />
        <span className={`font-medium ${status.color}`}>
          {status.text}
        </span>
        
        {/* Animated indicator for active states */}
        {(isRecording || isPlaying || isConnecting) && (
          <div className="flex space-x-1">
            <div className={`w-1 h-1 rounded-full ${status.color.replace('text-', 'bg-')} animate-bounce`}></div>
            <div className={`w-1 h-1 rounded-full ${status.color.replace('text-', 'bg-')} animate-bounce`} style={{ animationDelay: '0.1s' }}></div>
            <div className={`w-1 h-1 rounded-full ${status.color.replace('text-', 'bg-')} animate-bounce`} style={{ animationDelay: '0.2s' }}></div>
          </div>
        )}
      </div>
    </div>
  )
}
