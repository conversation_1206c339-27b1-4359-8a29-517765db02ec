import { ReactNode } from 'react'

interface AnimatedBackgroundProps {
  variant?: 'warm' | 'cool' | 'neutral' | 'aura' | 'sunbeam' | 'bliss'
  intensity?: 'low' | 'medium' | 'high'
  className?: string
  children?: ReactNode
}

const BACKGROUND_VARIANTS = {
  warm: {
    base: 'from-ardent-50 via-sunbeam-100 to-bliss-100',
    blobs: [
      'from-aura-200 to-aura-300',
      'from-sunbeam-200 to-sunbeam-300',
      'from-bliss-200 to-bliss-300'
    ]
  },
  cool: {
    base: 'from-ardent-50 via-bliss-100 to-sunbeam-100',
    blobs: [
      'from-bliss-200 to-bliss-300',
      'from-ardent-100 to-ardent-200',
      'from-sunbeam-100 to-sunbeam-200'
    ]
  },
  neutral: {
    base: 'from-gray-50 via-ardent-50 to-bliss-50',
    blobs: [
      'from-gray-200 to-gray-300',
      'from-ardent-100 to-ardent-200',
      'from-bliss-100 to-bliss-200'
    ]
  },
  aura: {
    base: 'from-aura-50 via-aura-100 to-sunbeam-100',
    blobs: [
      'from-aura-200 to-aura-400',
      'from-aura-300 to-sunbeam-300',
      'from-sunbeam-200 to-aura-200'
    ]
  },
  sunbeam: {
    base: 'from-sunbeam-50 via-sunbeam-100 to-bliss-100',
    blobs: [
      'from-sunbeam-200 to-sunbeam-400',
      'from-sunbeam-300 to-bliss-300',
      'from-bliss-200 to-sunbeam-200'
    ]
  },
  bliss: {
    base: 'from-bliss-50 via-bliss-100 to-ardent-100',
    blobs: [
      'from-bliss-200 to-bliss-400',
      'from-bliss-300 to-ardent-200',
      'from-ardent-100 to-bliss-200'
    ]
  }
}

const INTENSITY_SETTINGS = {
  low: {
    blobCount: 2,
    blobSizes: ['w-64 h-64', 'w-80 h-80'],
    opacity: 'opacity-40',
    blur: 'blur-2xl'
  },
  medium: {
    blobCount: 3,
    blobSizes: ['w-72 h-72', 'w-96 h-96', 'w-80 h-80'],
    opacity: 'opacity-60',
    blur: 'blur-xl'
  },
  high: {
    blobCount: 4,
    blobSizes: ['w-80 h-80', 'w-96 h-96', 'w-72 h-72', 'w-88 h-88'],
    opacity: 'opacity-70',
    blur: 'blur-xl'
  }
}

const BLOB_POSITIONS = [
  'top-10 left-10',
  'top-20 right-10',
  'bottom-10 left-1/3',
  'bottom-20 right-1/4'
]

export default function AnimatedBackground({
  variant = 'warm',
  intensity = 'medium',
  className = '',
  children
}: AnimatedBackgroundProps) {
  const backgroundConfig = BACKGROUND_VARIANTS[variant]
  const intensityConfig = INTENSITY_SETTINGS[intensity]

  return (
    <div className={`relative min-h-screen overflow-hidden ${className}`}>
      {/* Base gradient background */}
      <div className={`absolute inset-0 bg-gradient-to-br ${backgroundConfig.base}`} />

      {/* Animated floating blobs */}
      <div className="absolute inset-0">
        {Array.from({ length: intensityConfig.blobCount }, (_, i) => (
          <div
            key={i}
            className={`
              absolute rounded-full 
              bg-gradient-to-r ${backgroundConfig.blobs[i % backgroundConfig.blobs.length]}
              ${intensityConfig.blobSizes[i % intensityConfig.blobSizes.length]}
              ${intensityConfig.opacity}
              ${intensityConfig.blur}
              ${BLOB_POSITIONS[i % BLOB_POSITIONS.length]}
              mix-blend-multiply
              animate-blob
            `}
            style={{
              animationDelay: `${i * 2}s`,
              animationDuration: `${7 + i}s`
            }}
          />
        ))}
      </div>

      {/* Subtle overlay for better text readability */}
      <div className="absolute inset-0 bg-white/10" />

      {/* Content */}
      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </div>
  )
}

// Preset background components for common use cases
export function WelcomeBackground({ children }: { children?: ReactNode }) {
  return (
    <AnimatedBackground variant="warm" intensity="medium">
      {children}
    </AnimatedBackground>
  )
}

export function ChatBackground({ children }: { children?: ReactNode }) {
  return (
    <AnimatedBackground variant="cool" intensity="low">
      {children}
    </AnimatedBackground>
  )
}

export function VoiceBackground({ children }: { children?: ReactNode }) {
  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Subtle ambient glow */}
      <div className="absolute inset-0 bg-gradient-radial from-blue-900/20 via-transparent to-transparent" />

      {/* Content */}
      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </div>
  )
}

export function OnboardingBackground({ children }: { children?: ReactNode }) {
  return (
    <AnimatedBackground variant="warm" intensity="medium">
      {children}
    </AnimatedBackground>
  )
}
