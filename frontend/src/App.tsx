import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'
import { LoadingSpinner } from './components/ui/LoadingSpinner'

// Pages
import LoginPage from './pages/LoginPage'
import OnboardingPage from './pages/OnboardingPage'
import HomePage from './pages/HomePage'
import VoiceInterfacePage from './pages/VoiceInterfacePage'
import DashboardPage from './pages/DashboardPage'
import ChatPage from './pages/ChatPage'
import ProfilePage from './pages/ProfilePage'
import AuthCallbackPage from './pages/AuthCallbackPage'
import AuthErrorPage from './pages/AuthErrorPage'

// Layout
import Layout from './components/layout/Layout'

function App() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center gradient-warm">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  // Check if user needs onboarding
  const needsOnboarding = user && !user.profileData?.onboardingCompleted

  return (
    <Routes>
      {/* Public routes */}
      <Route path="/login" element={!user ? <LoginPage /> : <Navigate to={needsOnboarding ? "/onboarding" : "/dashboard"} replace />} />
      <Route path="/auth/callback" element={<AuthCallbackPage />} />
      <Route path="/auth/error" element={<AuthErrorPage />} />

      {/* Onboarding route */}
      <Route path="/onboarding" element={user ? (needsOnboarding ? <OnboardingPage /> : <Navigate to="/dashboard" replace />) : <Navigate to="/login" replace />} />

      {/* Protected routes */}
      <Route path="/" element={user ? (needsOnboarding ? <Navigate to="/onboarding" replace /> : <HomePage />) : <Navigate to="/login" replace />} />

      {/* Voice Interface Route */}
      <Route path="/voice" element={user ? (needsOnboarding ? <Navigate to="/onboarding" replace /> : <VoiceInterfacePage />) : <Navigate to="/login" replace />} />

      {/* Additional protected routes with layout */}
      <Route path="/dashboard" element={user ? (needsOnboarding ? <Navigate to="/onboarding" replace /> : <Layout />) : <Navigate to="/login" replace />}>
        <Route index element={<DashboardPage />} />
      </Route>
      <Route path="/chat" element={user ? (needsOnboarding ? <Navigate to="/onboarding" replace /> : <Layout />) : <Navigate to="/login" replace />}>
        <Route index element={<ChatPage />} />
      </Route>
      <Route path="/profile" element={user ? (needsOnboarding ? <Navigate to="/onboarding" replace /> : <Layout />) : <Navigate to="/login" replace />}>
        <Route index element={<ProfilePage />} />
      </Route>

      {/* Catch all */}
      <Route path="*" element={<Navigate to={user ? (needsOnboarding ? "/onboarding" : "/") : "/login"} replace />} />
    </Routes>
  )
}

export default App
