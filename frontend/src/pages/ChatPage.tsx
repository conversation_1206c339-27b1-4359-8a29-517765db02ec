import { useEffect, useMemo } from 'react'
import { useChat } from '../contexts/ChatContext'
import { useToast } from '../components/ui/Toaster'
import { Mic, PhoneOff } from 'lucide-react'
import VoiceOrb from '../components/voice/VoiceOrb'
import FloatingBubbles from '../components/voice/FloatingBubbles'
import { useEmotionVisuals } from '../hooks/useEmotionVisuals'
import {
  VoiceInterfaceErrorBoundary,
  usePerformanceMonitor,
  useReducedMotion,
  accessibilityUtils
} from '../utils/performanceOptimization'

export default function ChatPage() {
  const {
    isConnected,
    isConnecting,
    connectionError,
    isChatActive,
    messages,
    isRecording,
    isPlaying,
    connect,
    disconnect: _disconnect,
    startChat,
    endChat
  } = useChat()

  const { addToast } = useToast()

  // Performance and accessibility monitoring
  const performanceMetrics = usePerformanceMonitor()
  const prefersReducedMotion = useReducedMotion()

  // Get latest emotions from messages for real-time visual feedback
  const latestEmotions = useMemo(() => {
    if (messages.length === 0) return {}
    const latestMessage = messages[messages.length - 1]
    return latestMessage?.emotions || {}
  }, [messages])

  // Use the enhanced emotion visuals system with performance optimizations
  const emotionVisuals = useEmotionVisuals(
    latestEmotions,
    isChatActive,
    {
      smoothingFactor: performanceMetrics.shouldReduceEffects ? 0.9 : 0.8,
      transitionDuration: prefersReducedMotion ? 500 : 1500,
      intensityMultiplier: performanceMetrics.shouldReduceEffects ? 0.8 : 1.2
    }
  )

  useEffect(() => {
    // Auto-connect when component mounts
    if (!isConnected && !isConnecting) {
      handleConnect()
    }

    return () => {
      // Cleanup handled by audio service
    }
  }, [])

  const handleConnect = async () => {
    try {
      await connect()
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Connection Failed',
        message: 'Failed to connect to chat server'
      })
    }
  }

  const handleStartChat = async () => {
    try {
      // Start chat (this will handle microphone access and audio setup)
      await startChat()
      accessibilityUtils.announceVoiceState('listening')
    } catch (error) {
      console.error('Failed to start chat:', error)
      addToast({
        type: 'error',
        title: 'Microphone Access Required',
        message: 'Please allow microphone access to start voice chat'
      })
      accessibilityUtils.announceToScreenReader('Failed to start voice chat. Please check microphone permissions.')
    }
  }

  const handleEndChat = () => {
    endChat()
    accessibilityUtils.announceVoiceState('idle')
  }

  // Announce voice state changes for accessibility
  useEffect(() => {
    if (isRecording) {
      accessibilityUtils.announceVoiceState('listening')
    } else if (isPlaying) {
      accessibilityUtils.announceVoiceState('speaking')
    } else if (isChatActive && !isRecording && !isPlaying) {
      accessibilityUtils.announceVoiceState('processing')
    }
  }, [isRecording, isPlaying, isChatActive])

  return (
    <VoiceInterfaceErrorBoundary>
      <div
        className="h-screen w-full bg-white relative overflow-hidden"
        role="main"
        aria-label="Voice chat interface"
      >
        {/* Floating Bubbles Background */}
        <FloatingBubbles
          emotions={emotionVisuals.currentEmotions}
          intensity={emotionVisuals.intensity}
          bubbleCount={performanceMetrics.shouldReduceEffects ? 15 : (isChatActive ? 35 : 20)}
          isActive={isChatActive && !prefersReducedMotion}
          className="absolute inset-0"
        />

        {/* Main Content */}
        <div className="relative z-10 h-full flex flex-col items-center justify-center">
          {/* Voice Orb */}
          <div className="mb-12">
            <VoiceOrb
              emotions={emotionVisuals.currentEmotions}
              isListening={isRecording}
              isProcessing={isChatActive && !isRecording && !isPlaying}
              isSpeaking={isPlaying}
              isIdle={!isChatActive}
              intensity={emotionVisuals.intensity}
              size="large"
            />
          </div>

          {/* Voice State Indicator */}
          <div className="mb-8 text-center">
            {!isConnected ? (
              <div className="text-eclipse-950/60">
                <div className="animate-pulse mb-2">Connecting to ORA...</div>
              </div>
            ) : !isChatActive ? (
              <div className="text-eclipse-950/80">
                <div className="text-lg font-medium mb-2">Ready to chat with ORA</div>
                <div className="text-sm text-eclipse-950/60">Tap the orb to begin your voice conversation</div>
              </div>
            ) : isRecording ? (
              <div className="text-aura-600">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-aura-600 rounded-full animate-pulse"></div>
                  <span className="text-lg font-medium">Listening...</span>
                </div>
                <div className="text-sm text-eclipse-950/60">Speak naturally, ORA is listening</div>
              </div>
            ) : isPlaying ? (
              <div className="text-sunbeam-600">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-sunbeam-600 rounded-full animate-pulse"></div>
                  <span className="text-lg font-medium">ORA is speaking...</span>
                </div>
                <div className="text-sm text-eclipse-950/60">Listen to ORA's response</div>
              </div>
            ) : (
              <div className="text-bliss-600">
                <div className="text-lg font-medium mb-2">Processing...</div>
                <div className="text-sm text-eclipse-950/60">ORA is thinking about your message</div>
              </div>
            )}
          </div>

          {/* Voice Controls */}
          <div className="flex items-center justify-center space-x-6">
            {!isChatActive ? (
              <button
                onClick={handleStartChat}
                onKeyDown={(e) => accessibilityUtils.handleKeyboardNavigation(e, handleStartChat)}
                disabled={!isConnected}
                aria-label="Start voice conversation with ORA"
                aria-describedby="voice-status"
                className={`
                  w-20 h-20 rounded-full flex items-center justify-center
                  transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-aura-300
                  ${isConnected
                    ? 'bg-gradient-to-r from-aura-500 to-sunbeam-500 text-white shadow-lg hover:shadow-xl'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }
                `}
              >
                <Mic className="w-8 h-8" />
              </button>
            ) : (
              <button
                onClick={handleEndChat}
                onKeyDown={(e) => accessibilityUtils.handleKeyboardNavigation(e, handleEndChat)}
                aria-label="End voice conversation"
                className="
                  w-16 h-16 rounded-full flex items-center justify-center
                  bg-gradient-to-r from-eclipse-600 to-aura-600 text-white
                  transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-eclipse-300
                  shadow-lg hover:shadow-xl
                "
              >
                <PhoneOff className="w-6 h-6" />
              </button>
            )}
          </div>

          {/* Connection Error */}
          {connectionError && (
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg">
                <p className="text-sm text-red-800">{connectionError}</p>
              </div>
            </div>
          )}

          {/* Subtle conversation count indicator */}
          {messages.length > 0 && (
            <div className="absolute top-8 right-8">
              <div className="bg-white/80 backdrop-blur-sm rounded-full px-3 py-1 text-xs text-eclipse-950/60">
                {messages.length} exchange{messages.length !== 1 ? 's' : ''}
              </div>
            </div>
          )}

          {/* Performance indicator for development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="absolute top-8 left-8">
              <div className={`
                bg-white/80 backdrop-blur-sm rounded-full px-3 py-1 text-xs
                ${performanceMetrics.isOptimal ? 'text-green-600' : 'text-red-600'}
              `}>
                {performanceMetrics.fps} FPS
              </div>
            </div>
          )}

          {/* Screen reader status updates */}
          <div id="voice-status" className="sr-only" aria-live="polite" aria-atomic="true">
            {!isConnected ? 'Connecting to ORA...' :
             !isChatActive ? 'Voice chat ready. Press Enter or Space to start.' :
             isRecording ? 'ORA is listening. Speak now.' :
             isPlaying ? 'ORA is responding.' :
             'ORA is processing your message.'}
          </div>
        </div>
      </div>
    </VoiceInterfaceErrorBoundary>
  )
}
