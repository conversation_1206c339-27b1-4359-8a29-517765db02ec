
import { Link } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { MessageSquare, TrendingUp, Clock, Heart } from 'lucide-react'

export default function DashboardPage() {
  const { user } = useAuth()

  const stats = [
    {
      name: 'Total Conversations',
      value: '0',
      icon: MessageSquare,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      name: 'Average Session Time',
      value: '0 min',
      icon: Clock,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      name: 'Emotion Insights',
      value: '0',
      icon: Heart,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    },
    {
      name: 'Growth Trend',
      value: '+0%',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    }
  ]

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.name?.split(' ')[0]}!
        </h1>
        <p className="text-gray-600 mt-2">
          Here's an overview of your empathic AI conversations
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => {
          const Icon = stat.icon
          return (
            <div key={stat.name} className="card">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Start Chat */}
        <div className="card">
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center mb-4">
              <MessageSquare className="h-8 w-8 text-primary-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Start a Conversation
            </h3>
            <p className="text-gray-600 mb-6">
              Begin a new empathic voice conversation with our AI
            </p>
            <Link
              to="/chat"
              className="btn-primary inline-flex items-center"
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              Start Chat
            </Link>
          </div>
        </div>

        {/* Profile Setup */}
        <div className="card">
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-secondary-100 rounded-full flex items-center justify-center mb-4">
              <Heart className="h-8 w-8 text-secondary-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Personalize Your Experience
            </h3>
            <p className="text-gray-600 mb-6">
              Tell us about yourself to get more personalized conversations
            </p>
            <Link
              to="/profile"
              className="btn-secondary inline-flex items-center"
            >
              <Heart className="w-4 h-4 mr-2" />
              Update Profile
            </Link>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="mt-8">
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Recent Activity
          </h3>
          <div className="text-center py-12">
            <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              No conversations yet
            </h4>
            <p className="text-gray-600 mb-6">
              Start your first conversation to see your activity here
            </p>
            <Link to="/chat" className="btn-primary">
              Start Your First Chat
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
