import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'
import { useToast } from '../components/ui/Toaster'

declare global {
  interface Window {
    google: any
  }
}

export default function LoginPage() {
  const navigate = useNavigate()
  const { login } = useAuth()
  const { addToast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Initialize Google Sign-In
    if (window.google) {
      initializeGoogleSignIn()
    } else {
      // Wait for Google script to load
      const checkGoogle = setInterval(() => {
        if (window.google) {
          clearInterval(checkGoogle)
          initializeGoogleSignIn()
        }
      }, 100)
    }
  }, [])

  const initializeGoogleSignIn = () => {
    window.google.accounts.id.initialize({
      client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
      callback: handleGoogleSignIn,
      auto_select: false,
      cancel_on_tap_outside: true
    })

    window.google.accounts.id.renderButton(
      document.getElementById('google-signin-button'),
      {
        theme: 'outline',
        size: 'large',
        width: 300,
        text: 'signin_with',
        shape: 'rectangular'
      }
    )
  }

  const handleGoogleSignIn = async (response: any) => {
    try {
      setIsLoading(true)
      await login(response.credential)
      
      addToast({
        type: 'success',
        title: 'Welcome!',
        message: 'Successfully signed in with Google'
      })
      
      navigate('/dashboard')
    } catch (error) {
      console.error('Login failed:', error)
      addToast({
        type: 'error',
        title: 'Login Failed',
        message: error instanceof Error ? error.message : 'Failed to sign in with Google'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen gradient-warm flex items-center justify-center p-4">
      {/* Dynamic background blobs */}
      <div className="dynamic-background">
        <div className="background-blob floating-blob-1 w-72 h-72 bg-aura-200 top-10 left-10"></div>
        <div className="background-blob floating-blob-2 w-96 h-96 bg-sunbeam-200 top-20 right-10"></div>
        <div className="background-blob floating-blob-3 w-80 h-80 bg-bliss-200 bottom-10 left-1/3"></div>
      </div>

      <div className="max-w-md w-full space-y-8 relative z-10">
        <div className="text-center">
          <div className="mx-auto h-20 w-20 bg-gradient-to-r from-aura-900 to-sunbeam-900 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
            <span className="text-white font-bold text-2xl">ORA</span>
          </div>
          <h2 className="text-4xl font-bold text-eclipse-950">Welcome to ORA</h2>
          <p className="mt-3 text-lg text-eclipse-950/80">
            Your empathic AI companion that understands emotions and creates meaningful conversations
          </p>
        </div>

        <div className="card-glass space-y-6">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-eclipse-950 mb-2">
              Sign in to get started
            </h3>
            <p className="text-eclipse-950/70 mb-6">
              Connect with your empathic AI companion
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex justify-center">
              {isLoading ? (
                <div className="flex items-center justify-center w-[300px] h-[44px] bg-white/50 border border-aura-200 rounded-xl">
                  <LoadingSpinner size="sm" />
                  <span className="ml-2 text-sm text-eclipse-950">Signing in...</span>
                </div>
              ) : (
                <div id="google-signin-button"></div>
              )}
            </div>

            <div className="text-center">
              <p className="text-xs text-eclipse-950/60">
                By signing in, you agree to our{' '}
                <button className="text-aura-900 hover:underline font-medium">
                  Terms of Service
                </button>{' '}
                and{' '}
                <button className="text-aura-900 hover:underline font-medium">
                  Privacy Policy
                </button>
              </p>
            </div>
          </div>

          <div className="border-t border-white/20 pt-6">
            <div className="text-center space-y-3">
              <h4 className="text-sm font-medium text-eclipse-950">What you can do:</h4>
              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center space-x-2 text-xs text-eclipse-950/70">
                  <span className="text-aura-900">🎤</span>
                  <span>Have natural voice conversations with AI</span>
                </div>
                <div className="flex items-center space-x-2 text-xs text-eclipse-950/70">
                  <span className="text-sunbeam-900">💝</span>
                  <span>Get real-time emotion analysis</span>
                </div>
                <div className="flex items-center space-x-2 text-xs text-eclipse-950/70">
                  <span className="text-bliss-900">📊</span>
                  <span>View conversation insights</span>
                </div>
                <div className="flex items-center space-x-2 text-xs text-eclipse-950/70">
                  <span className="text-aura-900">⚙️</span>
                  <span>Customize your experience</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center">
          <p className="text-xs text-eclipse-950/50">
            Powered by{' '}
            <a
              href="https://hume.ai"
              target="_blank"
              rel="noopener noreferrer"
              className="text-aura-900 hover:underline font-medium"
            >
              Hume AI
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
