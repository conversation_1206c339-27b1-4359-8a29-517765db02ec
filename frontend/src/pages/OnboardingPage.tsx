import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { OnboardingProvider, useOnboarding } from '../contexts/OnboardingContext'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'

// Onboarding step components
import WelcomeStep from '../components/onboarding/WelcomeStep'
import NameStep from '../components/onboarding/NameStep'
import AgeStep from '../components/onboarding/AgeStep'
import PersonaStep from '../components/onboarding/PersonaStep'
import ConsentStep from '../components/onboarding/ConsentStep'

function OnboardingFlow() {
  const { user } = useAuth()
  const { currentStep, isLoading } = useOnboarding()
  const navigate = useNavigate()

  // Check if user has already completed onboarding
  useEffect(() => {
    if (user?.profileData?.onboardingCompleted) {
      navigate('/dashboard', { replace: true })
    }
  }, [user, navigate])

  // Show loading spinner during submission
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center gradient-warm">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-eclipse-950 font-medium">Setting up your profile...</p>
        </div>
      </div>
    )
  }

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return <WelcomeStep />
      case 1:
        return <NameStep />
      case 2:
        return <AgeStep />
      case 3:
        return <PersonaStep />
      case 4:
        return <ConsentStep />
      default:
        return <WelcomeStep />
    }
  }

  return (
    <div className="min-h-screen gradient-warm">
      {/* Dynamic background blobs */}
      <div className="dynamic-background">
        <div className="background-blob floating-blob-1 w-72 h-72 bg-aura-200 top-10 left-10"></div>
        <div className="background-blob floating-blob-2 w-96 h-96 bg-sunbeam-200 top-20 right-10"></div>
        <div className="background-blob floating-blob-3 w-80 h-80 bg-bliss-200 bottom-10 left-1/3"></div>
      </div>

      {/* Progress indicator */}
      <div className="relative z-10 pt-8">
        <div className="max-w-md mx-auto px-4">
          <div className="flex items-center justify-between mb-8">
            {Array.from({ length: 5 }, (_, i) => (
              <div
                key={i}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  i <= currentStep
                    ? 'bg-aura-900 scale-110'
                    : 'bg-white/50 scale-100'
                }`}
              />
            ))}
          </div>
          <div className="w-full bg-white/30 rounded-full h-2 mb-8">
            <div
              className="bg-gradient-to-r from-aura-900 to-sunbeam-900 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${((currentStep + 1) / 5) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Step content */}
      <div className="relative z-10 flex items-center justify-center min-h-[calc(100vh-200px)]">
        <div className="w-full max-w-md mx-auto px-4">
          {renderStep()}
        </div>
      </div>
    </div>
  )
}

export default function OnboardingPage() {
  return (
    <OnboardingProvider>
      <OnboardingFlow />
    </OnboardingProvider>
  )
}
