import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { AuthProvider } from '../contexts/AuthContext'
import { OnboardingProvider } from '../contexts/OnboardingContext'

// Mock components for testing
import WelcomeStep from '../components/onboarding/WelcomeStep'
import NameStep from '../components/onboarding/NameStep'
import AgeStep from '../components/onboarding/AgeStep'
import DynamicBackground from '../components/ui/DynamicBackground'
import AnimatedBackground from '../components/ui/AnimatedBackground'

// Mock API service
vi.mock('../services/api', () => ({
  apiService: {
    updateProfile: vi.fn().mockResolvedValue({ success: true }),
    getAccessToken: vi.fn().mockReturnValue('mock-token'),
    getCurrentUser: vi.fn().mockResolvedValue({
      success: true,
      data: {
        user: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          profileData: {}
        }
      }
    })
  }
}))

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    <AuthProvider>
      <OnboardingProvider>
        {children}
      </OnboardingProvider>
    </AuthProvider>
  </BrowserRouter>
)

describe('Onboarding Components', () => {
  it('renders WelcomeStep correctly', () => {
    render(
      <TestWrapper>
        <WelcomeStep />
      </TestWrapper>
    )
    
    expect(screen.getByText('Welcome to ORA')).toBeInTheDocument()
    expect(screen.getByText('Get Started')).toBeInTheDocument()
  })

  it('renders NameStep with validation', () => {
    render(
      <TestWrapper>
        <NameStep />
      </TestWrapper>
    )
    
    expect(screen.getByText('What should ORA call you?')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Enter your first name')).toBeInTheDocument()
  })

  it('validates name input correctly', () => {
    render(
      <TestWrapper>
        <NameStep />
      </TestWrapper>
    )
    
    const input = screen.getByPlaceholderText('Enter your first name')
    const continueButton = screen.getByText('Continue')
    
    // Test empty input
    expect(continueButton).toBeDisabled()
    
    // Test valid input
    fireEvent.change(input, { target: { value: 'John' } })
    expect(continueButton).not.toBeDisabled()
    
    // Test too long input
    fireEvent.change(input, { target: { value: 'A'.repeat(25) } })
    expect(screen.getByText(/Name must be 24 characters or less/)).toBeInTheDocument()
  })

  it('renders AgeStep with age ranges', () => {
    render(
      <TestWrapper>
        <AgeStep />
      </TestWrapper>
    )
    
    expect(screen.getByText('What\'s your age range?')).toBeInTheDocument()
    expect(screen.getByText('18-24')).toBeInTheDocument()
    expect(screen.getByText('25-34')).toBeInTheDocument()
  })
})

describe('Background Components', () => {
  it('renders DynamicBackground with emotions', () => {
    const emotions = { joy: 0.8, excitement: 0.6 }
    
    render(
      <DynamicBackground emotions={emotions} intensity={0.7}>
        <div>Test Content</div>
      </DynamicBackground>
    )
    
    expect(screen.getByText('Test Content')).toBeInTheDocument()
  })

  it('renders AnimatedBackground with different variants', () => {
    render(
      <AnimatedBackground variant="warm" intensity="high">
        <div>Background Test</div>
      </AnimatedBackground>
    )
    
    expect(screen.getByText('Background Test')).toBeInTheDocument()
  })
})

describe('Design System', () => {
  it('applies ORA color palette classes correctly', () => {
    render(
      <div className="bg-aura-900 text-white p-4">
        <span className="text-sunbeam-500">ORA Colors</span>
      </div>
    )
    
    // Test that Tailwind classes are applied (this would need actual CSS testing in a real environment)
    expect(true).toBe(true) // Placeholder assertion
  })
})

describe('Responsive Design', () => {
  it('handles mobile viewport correctly', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    })
    
    render(
      <TestWrapper>
        <WelcomeStep />
      </TestWrapper>
    )
    
    // Test that mobile-specific elements are present
    expect(screen.getByText('Welcome to ORA')).toBeInTheDocument()
  })

  it('handles desktop viewport correctly', () => {
    // Mock desktop viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    })
    
    render(
      <TestWrapper>
        <WelcomeStep />
      </TestWrapper>
    )
    
    expect(screen.getByText('Welcome to ORA')).toBeInTheDocument()
  })
})

describe('Accessibility', () => {
  it('has proper ARIA labels and roles', () => {
    render(
      <TestWrapper>
        <NameStep />
      </TestWrapper>
    )
    
    const input = screen.getByLabelText('First Name')
    expect(input).toHaveAttribute('type', 'text')
    expect(input).toHaveAttribute('maxLength', '24')
  })

  it('supports keyboard navigation', () => {
    render(
      <TestWrapper>
        <NameStep />
      </TestWrapper>
    )
    
    const input = screen.getByPlaceholderText('Enter your first name')
    
    // Test Enter key functionality
    fireEvent.change(input, { target: { value: 'John' } })
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' })
    
    // In a real test, we'd verify navigation occurred
    expect(input).toHaveValue('John')
  })
})

describe('Performance', () => {
  it('renders components without performance issues', () => {
    const startTime = performance.now()
    
    render(
      <TestWrapper>
        <DynamicBackground emotions={{ joy: 0.5 }}>
          <WelcomeStep />
        </DynamicBackground>
      </TestWrapper>
    )
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // Ensure render time is reasonable (less than 100ms)
    expect(renderTime).toBeLessThan(100)
  })
})
